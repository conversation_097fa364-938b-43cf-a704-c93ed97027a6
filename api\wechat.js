/**
 * 微信相关API接口
 */

import { getCurrentAPIConfig } from '@/utils/config.js'

// 获取API配置
const apiConfig = getCurrentAPIConfig()
const BASE_URL = apiConfig.baseURL

/**
 * 统一请求方法
 */
const request = (options) => {
  return new Promise((resolve, reject) => {
    uni.request({
      url: BASE_URL + options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Content-Type': 'application/json',
        ...options.header
      },
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data)
        } else {
          reject(new Error(`请求失败: ${res.statusCode}`))
        }
      },
      fail: (err) => {
        reject(err)
      }
    })
  })
}

/**
 * 微信API接口
 */
export const wechatAPI = {
  /**
   * 获取微信JS-SDK配置信息
   * @param {string} url - 当前页面URL
   */
  async getJsApiTicket(url) {
    try {
      const response = await request({
        url: '/api/wechat/getJsApiTicket',
        method: 'POST',
        data: {
          url: url || window.location.href.split('#')[0]
        }
      })
      return response
    } catch (error) {
      console.error('获取JS-SDK配置失败:', error)
      throw error
    }
  },

  /**
   * 获取access_token
   */
  async getAccessToken() {
    try {
      const response = await request({
        url: '/api/wechat/getAccessToken',
        method: 'GET'
      })
      return response
    } catch (error) {
      console.error('获取access_token失败:', error)
      throw error
    }
  }
}

/**
 * 后端API示例代码（需要在你的后端实现）
 * 
 * 以下是后端需要实现的API接口示例（Node.js Express）：
 * 
 * const crypto = require('crypto')
 * const axios = require('axios')
 * 
 * // 微信公众号配置
 * const WECHAT_CONFIG = {
 *   appId: 'wx50fb2779cc75a41f',
 *   appSecret: '8888'
 * }
 * 
 * // 获取access_token
 * async function getAccessToken() {
 *   const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${WECHAT_CONFIG.appId}&secret=${WECHAT_CONFIG.appSecret}`
 *   const response = await axios.get(url)
 *   return response.data.access_token
 * }
 * 
 * // 获取jsapi_ticket
 * async function getJsApiTicket(accessToken) {
 *   const url = `https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=${accessToken}&type=jsapi`
 *   const response = await axios.get(url)
 *   return response.data.ticket
 * }
 * 
 * // 生成签名
 * function generateSignature(ticket, nonceStr, timestamp, url) {
 *   const string = `jsapi_ticket=${ticket}&noncestr=${nonceStr}&timestamp=${timestamp}&url=${url}`
 *   return crypto.createHash('sha1').update(string).digest('hex')
 * }
 * 
 * // API接口
 * app.post('/api/wechat/getJsApiTicket', async (req, res) => {
 *   try {
 *     const { url } = req.body
 *     
 *     // 获取access_token
 *     const accessToken = await getAccessToken()
 *     
 *     // 获取jsapi_ticket
 *     const ticket = await getJsApiTicket(accessToken)
 *     
 *     // 生成随机字符串和时间戳
 *     const nonceStr = Math.random().toString(36).substr(2, 15)
 *     const timestamp = parseInt(new Date().getTime() / 1000)
 *     
 *     // 生成签名
 *     const signature = generateSignature(ticket, nonceStr, timestamp, url)
 *     
 *     res.json({
 *       code: 0,
 *       data: {
 *         appId: WECHAT_CONFIG.appId,
 *         timestamp,
 *         nonceStr,
 *         signature
 *       }
 *     })
 *   } catch (error) {
 *     console.error('获取JS-SDK配置失败:', error)
 *     res.json({
 *       code: -1,
 *       message: '获取JS-SDK配置失败'
 *     })
 *   }
 * })
 */

export default wechatAPI
