/**
 * 应用配置文件
 */

// 微信公众号配置
export const WECHAT_CONFIG = {
  appId: 'wx50fb2779cc75a41f', // 你的微信公众号AppID
  appSecret: '8888', // 你的微信公众号AppSecret（注意：前端不应该暴露AppSecret）
  
  // JS-SDK需要的权限列表
  jsApiList: [
    'scanQRCode',      // 扫码
    'chooseImage',     // 选择图片
    'uploadImage',     // 上传图片
    'downloadImage',   // 下载图片
    'getLocation',     // 获取地理位置
    'openLocation',    // 打开地图
    'getNetworkType',  // 获取网络状态
    'hideMenuItems',   // 隐藏菜单项
    'showMenuItems',   // 显示菜单项
    'hideAllNonBaseMenuItem', // 隐藏所有非基础菜单项
    'showAllNonBaseMenuItem'  // 显示所有非基础菜单项
  ]
}

// API配置
export const API_CONFIG = {
  // 开发环境
  development: {
    baseURL: 'http://localhost:3000',
    timeout: 10000
  },
  
  // 测试环境
  testing: {
    baseURL: 'https://test-api.your-domain.com',
    timeout: 10000
  },
  
  // 生产环境
  production: {
    baseURL: 'https://api.your-domain.com',
    timeout: 10000
  }
}

// 获取当前环境的API配置
export const getCurrentAPIConfig = () => {
  // 根据当前环境返回对应配置
  const env = process.env.NODE_ENV || 'development'
  return API_CONFIG[env] || API_CONFIG.development
}

// 应用基础配置
export const APP_CONFIG = {
  name: '智能用水系统',
  version: '1.0.0',
  
  // 页面配置
  pages: {
    scanWater: '/pages/scanWater/index',
    userInfo: '/pages/userInfo/index',
    transactionRecord: '/pages/transactionRecord/index'
  },
  
  // 功能开关
  features: {
    wechatSDK: true,        // 是否启用微信JS-SDK
    debugMode: false,       // 是否开启调试模式
    mockData: false         // 是否使用模拟数据
  }
}

export default {
  WECHAT_CONFIG,
  API_CONFIG,
  APP_CONFIG,
  getCurrentAPIConfig
}
