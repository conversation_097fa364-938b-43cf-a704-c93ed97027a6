<template>
  <view class="wx-test-container">
    <view class="header">
      <text class="title">微信JS-SDK测试页面</text>
    </view>
    
    <!-- 环境信息 -->
    <view class="info-section">
      <view class="info-item">
        <text class="label">当前环境：</text>
        <text class="value" :class="{ 'wechat': isWechat, 'normal': !isWechat }">
          {{ isWechat ? '微信浏览器' : '普通浏览器' }}
        </text>
      </view>
      <view class="info-item">
        <text class="label">SDK状态：</text>
        <text class="value" :class="{ 'ready': isSDKReady, 'error': !isSDKReady && !loading }">
          {{ loading ? '初始化中...' : isSDKReady ? '已就绪' : '未就绪' }}
        </text>
      </view>
      <view class="info-item">
        <text class="label">页面URL：</text>
        <text class="value small">{{ currentUrl }}</text>
      </view>
    </view>
    
    <!-- 功能测试按钮 -->
    <view class="test-section">
      <view class="section-title">功能测试</view>
      
      <view class="test-btn" @click="testScanCode" :class="{ 'disabled': loading }">
        <text class="btn-icon">📷</text>
        <text class="btn-text">扫码测试</text>
      </view>
      
      <view class="test-btn" @click="testGetLocation" :class="{ 'disabled': loading }">
        <text class="btn-icon">📍</text>
        <text class="btn-text">获取位置</text>
      </view>
      
      <view class="test-btn" @click="testChooseImage" :class="{ 'disabled': loading }">
        <text class="btn-icon">🖼️</text>
        <text class="btn-text">选择图片</text>
      </view>
      
      <view class="test-btn" @click="reinitSDK" :class="{ 'disabled': loading }">
        <text class="btn-icon">🔄</text>
        <text class="btn-text">重新初始化</text>
      </view>
    </view>
    
    <!-- 测试结果 -->
    <view class="result-section" v-if="testResults.length > 0">
      <view class="section-title">测试结果</view>
      <view class="result-list">
        <view 
          class="result-item" 
          v-for="(result, index) in testResults" 
          :key="index"
          :class="{ 'success': result.success, 'error': !result.success }"
        >
          <view class="result-header">
            <text class="result-title">{{ result.title }}</text>
            <text class="result-time">{{ result.time }}</text>
          </view>
          <view class="result-content">
            <text class="result-text">{{ result.message }}</text>
          </view>
        </view>
      </view>
      
      <view class="clear-btn" @click="clearResults">
        <text>清空结果</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import wechatSDK from '@/utils/wechat.js'

// 响应式数据
const isWechat = ref(false)
const isSDKReady = ref(false)
const loading = ref(false)
const currentUrl = ref('')
const testResults = ref([])

// 生命周期
onLoad(() => {
  currentUrl.value = window.location.href
  initWechatSDK()
})

// 初始化微信JS-SDK
const initWechatSDK = async () => {
  try {
    isWechat.value = wechatSDK.isWechatBrowser()
    
    if (isWechat.value) {
      loading.value = true
      addTestResult('SDK初始化', '开始初始化微信JS-SDK...', true)
      
      await wechatSDK.init()
      isSDKReady.value = true
      
      addTestResult('SDK初始化', '微信JS-SDK初始化成功', true)
    } else {
      addTestResult('环境检测', '当前不在微信浏览器环境中', false)
    }
  } catch (error) {
    console.error('微信JS-SDK初始化失败:', error)
    addTestResult('SDK初始化', `初始化失败: ${error.message}`, false)
  } finally {
    loading.value = false
  }
}

// 重新初始化SDK
const reinitSDK = async () => {
  if (loading.value) return
  
  isSDKReady.value = false
  await initWechatSDK()
}

// 测试扫码功能
const testScanCode = async () => {
  if (loading.value) return
  
  try {
    addTestResult('扫码测试', '开始扫码...', true)
    
    const result = await wechatSDK.scanQRCode({
      needResult: true,
      scanType: ["qrCode", "barCode"]
    })
    
    addTestResult('扫码测试', `扫码成功: ${result}`, true)
  } catch (error) {
    console.error('扫码失败:', error)
    addTestResult('扫码测试', `扫码失败: ${error.message}`, false)
  }
}

// 测试获取位置
const testGetLocation = async () => {
  if (loading.value) return
  
  try {
    addTestResult('位置测试', '开始获取位置...', true)
    
    const location = await wechatSDK.getLocation()
    
    addTestResult('位置测试', 
      `位置获取成功: 纬度${location.latitude}, 经度${location.longitude}`, 
      true
    )
  } catch (error) {
    console.error('获取位置失败:', error)
    addTestResult('位置测试', `获取位置失败: ${error.message}`, false)
  }
}

// 测试选择图片
const testChooseImage = async () => {
  if (loading.value) return
  
  try {
    addTestResult('图片测试', '开始选择图片...', true)
    
    const images = await wechatSDK.chooseImage({
      count: 1,
      sizeType: ['original', 'compressed'],
      sourceType: ['album', 'camera']
    })
    
    addTestResult('图片测试', 
      `图片选择成功: 共选择${images.length}张图片`, 
      true
    )
  } catch (error) {
    console.error('选择图片失败:', error)
    addTestResult('图片测试', `选择图片失败: ${error.message}`, false)
  }
}

// 添加测试结果
const addTestResult = (title, message, success) => {
  const now = new Date()
  const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
  
  testResults.value.unshift({
    title,
    message,
    success,
    time
  })
  
  // 限制结果数量
  if (testResults.value.length > 20) {
    testResults.value = testResults.value.slice(0, 20)
  }
}

// 清空测试结果
const clearResults = () => {
  testResults.value = []
}
</script>

<style lang="scss" scoped>
.wx-test-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.info-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
}

.value {
  font-size: 28rpx;
  flex: 1;
  
  &.wechat {
    color: #52c41a;
    font-weight: bold;
  }
  
  &.normal {
    color: #faad14;
  }
  
  &.ready {
    color: #52c41a;
    font-weight: bold;
  }
  
  &.error {
    color: #ff4d4f;
  }
  
  &.small {
    font-size: 24rpx;
    word-break: break-all;
  }
}

.test-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.test-btn {
  display: flex;
  align-items: center;
  padding: 25rpx 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  &:active:not(.disabled) {
    transform: translateY(2rpx);
    box-shadow: 0 2rpx 6rpx rgba(102, 126, 234, 0.3);
  }
  
  &.disabled {
    opacity: 0.5;
    pointer-events: none;
  }
}

.btn-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
}

.btn-text {
  font-size: 28rpx;
  color: white;
  font-weight: 500;
}

.result-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.result-list {
  margin-bottom: 30rpx;
}

.result-item {
  padding: 20rpx;
  border-radius: 10rpx;
  margin-bottom: 15rpx;
  
  &.success {
    background: #f6ffed;
    border-left: 6rpx solid #52c41a;
  }
  
  &.error {
    background: #fff2f0;
    border-left: 6rpx solid #ff4d4f;
  }
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.result-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
}

.result-time {
  font-size: 22rpx;
  color: #999;
}

.result-content {
  margin-top: 10rpx;
}

.result-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

.clear-btn {
  text-align: center;
  padding: 20rpx;
  background: #f0f0f0;
  border-radius: 10rpx;
  color: #666;
  font-size: 26rpx;
}
</style>
