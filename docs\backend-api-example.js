/**
 * 后端API示例代码
 * 这个文件展示了如何在后端实现微信JS-SDK所需的API接口
 * 
 * 技术栈：Node.js + Express
 * 你可以根据自己的后端技术栈进行相应调整
 */

const express = require('express')
const crypto = require('crypto')
const axios = require('axios')
const cors = require('cors')

const app = express()

// 中间件
app.use(cors())
app.use(express.json())

// 微信公众号配置
const WECHAT_CONFIG = {
  appId: 'wx50fb2779cc75a41f',
  appSecret: '8888' // 注意：实际项目中应该从环境变量读取
}

// 缓存access_token和jsapi_ticket
let tokenCache = {
  access_token: null,
  access_token_expires: 0,
  jsapi_ticket: null,
  jsapi_ticket_expires: 0
}

/**
 * 获取access_token
 */
async function getAccessToken() {
  // 检查缓存是否有效
  const now = Date.now()
  if (tokenCache.access_token && now < tokenCache.access_token_expires) {
    return tokenCache.access_token
  }

  try {
    const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${WECHAT_CONFIG.appId}&secret=${WECHAT_CONFIG.appSecret}`
    const response = await axios.get(url)
    
    if (response.data.access_token) {
      // 缓存access_token（有效期7200秒，提前5分钟过期）
      tokenCache.access_token = response.data.access_token
      tokenCache.access_token_expires = now + (response.data.expires_in - 300) * 1000
      
      console.log('获取access_token成功')
      return response.data.access_token
    } else {
      throw new Error('获取access_token失败: ' + JSON.stringify(response.data))
    }
  } catch (error) {
    console.error('获取access_token失败:', error.message)
    throw error
  }
}

/**
 * 获取jsapi_ticket
 */
async function getJsApiTicket() {
  // 检查缓存是否有效
  const now = Date.now()
  if (tokenCache.jsapi_ticket && now < tokenCache.jsapi_ticket_expires) {
    return tokenCache.jsapi_ticket
  }

  try {
    const accessToken = await getAccessToken()
    const url = `https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=${accessToken}&type=jsapi`
    const response = await axios.get(url)
    
    if (response.data.ticket) {
      // 缓存jsapi_ticket（有效期7200秒，提前5分钟过期）
      tokenCache.jsapi_ticket = response.data.ticket
      tokenCache.jsapi_ticket_expires = now + (response.data.expires_in - 300) * 1000
      
      console.log('获取jsapi_ticket成功')
      return response.data.ticket
    } else {
      throw new Error('获取jsapi_ticket失败: ' + JSON.stringify(response.data))
    }
  } catch (error) {
    console.error('获取jsapi_ticket失败:', error.message)
    throw error
  }
}

/**
 * 生成签名
 */
function generateSignature(ticket, nonceStr, timestamp, url) {
  const string = `jsapi_ticket=${ticket}&noncestr=${nonceStr}&timestamp=${timestamp}&url=${url}`
  return crypto.createHash('sha1').update(string).digest('hex')
}

/**
 * 生成随机字符串
 */
function generateNonceStr(length = 16) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * API接口：获取JS-SDK配置信息
 */
app.post('/api/wechat/getJsApiTicket', async (req, res) => {
  try {
    const { url } = req.body
    
    if (!url) {
      return res.json({
        code: -1,
        message: 'URL参数不能为空'
      })
    }

    console.log('获取JS-SDK配置，URL:', url)
    
    // 获取jsapi_ticket
    const ticket = await getJsApiTicket()
    
    // 生成随机字符串和时间戳
    const nonceStr = generateNonceStr()
    const timestamp = Math.floor(Date.now() / 1000)
    
    // 生成签名
    const signature = generateSignature(ticket, nonceStr, timestamp, url)
    
    console.log('签名生成成功:', {
      timestamp,
      nonceStr,
      signature: signature.substring(0, 10) + '...'
    })
    
    res.json({
      code: 0,
      message: '获取成功',
      data: {
        appId: WECHAT_CONFIG.appId,
        timestamp,
        nonceStr,
        signature
      }
    })
  } catch (error) {
    console.error('获取JS-SDK配置失败:', error)
    res.json({
      code: -1,
      message: '获取JS-SDK配置失败: ' + error.message
    })
  }
})

/**
 * API接口：获取access_token（仅用于调试）
 */
app.get('/api/wechat/getAccessToken', async (req, res) => {
  try {
    const accessToken = await getAccessToken()
    res.json({
      code: 0,
      message: '获取成功',
      data: {
        access_token: accessToken,
        expires_at: new Date(tokenCache.access_token_expires).toISOString()
      }
    })
  } catch (error) {
    console.error('获取access_token失败:', error)
    res.json({
      code: -1,
      message: '获取access_token失败: ' + error.message
    })
  }
})

/**
 * 健康检查接口
 */
app.get('/api/health', (req, res) => {
  res.json({
    code: 0,
    message: '服务正常',
    data: {
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    }
  })
})

/**
 * 错误处理中间件
 */
app.use((error, req, res, next) => {
  console.error('服务器错误:', error)
  res.status(500).json({
    code: -1,
    message: '服务器内部错误'
  })
})

// 启动服务器
const PORT = process.env.PORT || 3000
app.listen(PORT, () => {
  console.log(`服务器启动成功，端口: ${PORT}`)
  console.log(`健康检查: http://localhost:${PORT}/api/health`)
})

/**
 * 部署说明：
 * 
 * 1. 安装依赖：
 *    npm install express axios cors
 * 
 * 2. 环境变量配置（推荐）：
 *    WECHAT_APP_ID=wx50fb2779cc75a41f
 *    WECHAT_APP_SECRET=8888
 *    PORT=3000
 * 
 * 3. 启动服务：
 *    node backend-api-example.js
 * 
 * 4. 测试接口：
 *    curl -X POST http://localhost:3000/api/wechat/getJsApiTicket \
 *         -H "Content-Type: application/json" \
 *         -d '{"url":"http://your-domain.com/pages/scanWater/index"}'
 * 
 * 5. 生产环境注意事项：
 *    - 使用HTTPS
 *    - 配置正确的CORS策略
 *    - 使用环境变量管理敏感信息
 *    - 添加请求频率限制
 *    - 添加日志记录
 *    - 使用Redis等缓存access_token和jsapi_ticket
 */
