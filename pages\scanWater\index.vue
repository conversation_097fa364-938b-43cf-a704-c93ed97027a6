<template>
	<view class="scan-water-container">
		<!-- 扫码用水按钮 -->
		<view class="function-btn" @click="scanWater" :class="{ 'loading': loading }">
			<text class="iconfont" v-if="!loading">&#xe600;</text>
			<text class="loading-icon" v-else>⟳</text>
			<text class="btn-text">{{ loading ? '初始化中...' : '扫码用水' }}</text>
		</view>

		<!-- 环境状态提示 -->
		<view class="status-info" v-if="isWechat">
			<text class="status-text" :class="{ 'ready': isSDKReady, 'error': !isSDKReady && !loading }">
				{{ isSDKReady ? '✓ 微信功能已就绪' : loading ? '正在初始化微信功能...' : '✗ 微信功能初始化失败' }}
			</text>
		</view>
		<view class="status-info" v-else>
			<text class="status-text">使用原生扫码功能</text>
		</view>
	</view>
</template>

<script setup>

import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import wechatSDK from '@/utils/wechat.js'

// 响应式数据
const isWechat = ref(false)
const isSDKReady = ref(false)
const loading = ref(false)

// 生命周期钩子
onLoad(() => {
	// 检测微信环境并初始化SDK
	initWechatSDK()
})

// 初始化微信JS-SDK
const initWechatSDK = async () => {
	try {
		isWechat.value = wechatSDK.isWechatBrowser()

		if (isWechat.value) {
			console.log('检测到微信环境，开始初始化JS-SDK...')
			loading.value = true

			await wechatSDK.init()
			isSDKReady.value = true

			console.log('微信JS-SDK初始化成功')
			uni.showToast({
				title: '微信功能已就绪',
				icon: 'success',
				duration: 2000
			})
		} else {
			console.log('非微信环境，将使用uni-app原生扫码')
		}
	} catch (error) {
		console.error('微信JS-SDK初始化失败:', error)
		uni.showToast({
			title: '微信功能初始化失败',
			icon: 'error',
			duration: 3000
		})
	} finally {
		loading.value = false
	}
}

// 开始扫码
const scanWater = async () => {
	if (loading.value) {
		uni.showToast({
			title: '正在初始化，请稍候',
			icon: 'loading'
		})
		return
	}

	try {
		let scanResult = ''

		if (isWechat.value && isSDKReady.value) {
			// 使用微信JS-SDK扫码
			console.log('使用微信扫码功能')
			scanResult = await wechatSDK.scanQRCode({
				needResult: true,
				scanType: ["qrCode", "barCode"]
			})
		} else {
			// 使用uni-app原生扫码
			console.log('使用uni-app原生扫码功能')
			const result = await uni.scanCode({
				scanType: ['qrCode', 'barCode']
			})
			scanResult = result.result
		}

		// 处理扫码结果
		handleScanResult(scanResult)

	} catch (error) {
		console.error('扫码失败:', error)

		if (error.errMsg && error.errMsg.includes('cancel')) {
			// 用户取消扫码
			console.log('用户取消扫码')
		} else {
			uni.showToast({
				title: '扫码失败，请重试',
				icon: 'error',
				duration: 2000
			})
		}
	}
}

// 处理扫码结果
const handleScanResult = (result) => {
	console.log('扫码结果:', result)

	// 这里可以根据扫码结果进行不同的处理
	// 比如解析二维码中的设备ID、用水参数等

	uni.showModal({
		title: '扫码成功',
		content: `扫码结果：${result}`,
		confirmText: '开始用水',
		cancelText: '重新扫码',
		success: function (res) {
			if (res.confirm) {
				// 用户点击开始用水
				startWaterUsage(result)
			} else if (res.cancel) {
				// 用户选择重新扫码
				scanWater()
			}
		}
	})
}

// 开始用水流程
const startWaterUsage = (qrCodeData) => {
	console.log('开始用水流程，二维码数据:', qrCodeData)

	// 这里实现具体的用水逻辑
	// 比如：
	// 1. 解析二维码获取设备信息
	// 2. 调用后端API开启设备
	// 3. 跳转到用水页面等

	uni.showToast({
		title: '正在启动设备...',
		icon: 'loading',
		duration: 2000
	})

	// 示例：跳转到用水页面
	// uni.navigateTo({
	//   url: `/pages/waterUsage/index?deviceId=${encodeURIComponent(qrCodeData)}`
	// })
}

</script>
<style lang="scss" scoped>
.scan-water-container {
	padding: 20rpx;
}

.function-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 30rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 20rpx;
	box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
	transition: all 0.3s ease;

	&:active {
		transform: translateY(2rpx);
		box-shadow: 0 4rpx 10rpx rgba(102, 126, 234, 0.3);
	}

	&.loading {
		opacity: 0.7;
		pointer-events: none;
	}
}

.iconfont {
	font-size: 44rpx;
	color: #ffffff;
	margin-right: 15rpx;
	line-height: 1;
	display: inline-block;
	width: 44rpx;
	text-align: center;
}

.loading-icon {
	font-size: 44rpx;
	color: #ffffff;
	margin-right: 15rpx;
	line-height: 1;
	display: inline-block;
	width: 44rpx;
	text-align: center;
	animation: rotate 1s linear infinite;
}

.btn-text {
	color: #ffffff;
	font-size: 32rpx;
	font-weight: 500;
}

.status-info {
	margin-top: 30rpx;
	text-align: center;
}

.status-text {
	font-size: 24rpx;
	color: #999;

	&.ready {
		color: #52c41a;
	}

	&.error {
		color: #ff4d4f;
	}
}

@keyframes rotate {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}
</style>
