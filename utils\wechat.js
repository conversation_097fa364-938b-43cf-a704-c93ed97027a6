/**
 * 微信JS-SDK工具类
 * 用于在微信公众号H5页面中调用微信原生功能
 */

import { WECHAT_CONFIG, APP_CONFIG } from './config.js'

class WechatJSSDK {
  constructor() {
    this.isWechat = this.isWechatBrowser()
    this.isConfigured = false
    this.wx = null
  }

  /**
   * 检测是否在微信浏览器中
   */
  isWechatBrowser() {
    const ua = navigator.userAgent.toLowerCase()
    return ua.indexOf('micromessenger') !== -1
  }

  /**
   * 动态加载微信JS-SDK
   */
  loadWechatSDK() {
    return new Promise((resolve, reject) => {
      if (window.wx) {
        this.wx = window.wx
        resolve(window.wx)
        return
      }

      const script = document.createElement('script')
      script.src = 'https://res.wx.qq.com/open/js/jweixin-1.6.0.js'
      script.onload = () => {
        this.wx = window.wx
        resolve(window.wx)
      }
      script.onerror = () => {
        reject(new Error('微信JS-SDK加载失败'))
      }
      document.head.appendChild(script)
    })
  }

  /**
   * 获取微信配置信息
   * @param {string} url - 当前页面URL
   */
  async getWechatConfig(url) {
    try {
      // 导入API模块
      const { wechatAPI } = await import('../api/wechat.js')

      // 调用API获取签名信息
      const response = await wechatAPI.getJsApiTicket(url || window.location.href.split('#')[0])

      if (response.code === 0) {
        return response.data
      } else {
        throw new Error(response.message || '获取微信配置失败')
      }
    } catch (error) {
      console.error('获取微信配置失败:', error)
      throw error
    }
  }

  /**
   * 配置微信JS-SDK
   * @param {Object} config - 配置信息
   */
  async configWechat(config) {
    if (!this.isWechat) {
      throw new Error('当前不在微信浏览器环境中')
    }

    await this.loadWechatSDK()

    return new Promise((resolve, reject) => {
      this.wx.config({
        debug: APP_CONFIG.features.debugMode, // 开启调试模式
        appId: config.appId || WECHAT_CONFIG.appId, // 你的appId
        timestamp: config.timestamp,
        nonceStr: config.nonceStr,
        signature: config.signature,
        jsApiList: WECHAT_CONFIG.jsApiList
      })

      this.wx.ready(() => {
        this.isConfigured = true
        console.log('微信JS-SDK配置成功')
        resolve()
      })

      this.wx.error((res) => {
        console.error('微信JS-SDK配置失败:', res)
        reject(new Error('微信JS-SDK配置失败: ' + JSON.stringify(res)))
      })
    })
  }

  /**
   * 初始化微信JS-SDK
   */
  async init() {
    if (!this.isWechat) {
      console.warn('当前不在微信浏览器环境中，跳过微信JS-SDK初始化')
      return false
    }

    try {
      const config = await this.getWechatConfig()
      await this.configWechat(config)
      return true
    } catch (error) {
      console.error('微信JS-SDK初始化失败:', error)
      throw error
    }
  }

  /**
   * 扫码功能
   * @param {Object} options - 扫码选项
   */
  scanQRCode(options = {}) {
    return new Promise((resolve, reject) => {
      if (!this.isWechat) {
        reject(new Error('当前不在微信浏览器环境中'))
        return
      }

      if (!this.isConfigured) {
        reject(new Error('微信JS-SDK未配置'))
        return
      }

      this.wx.scanQRCode({
        needResult: options.needResult !== false ? 1 : 0, // 默认返回扫码结果
        scanType: options.scanType || ["qrCode", "barCode"], // 可以指定扫二维码还是一维码，默认二者都有
        success: (res) => {
          console.log('扫码成功:', res)
          resolve(res.resultStr || res)
        },
        fail: (err) => {
          console.error('扫码失败:', err)
          reject(err)
        }
      })
    })
  }

  /**
   * 获取地理位置
   */
  getLocation() {
    return new Promise((resolve, reject) => {
      if (!this.isWechat || !this.isConfigured) {
        reject(new Error('微信JS-SDK未初始化'))
        return
      }

      this.wx.getLocation({
        type: 'wgs84', // 默认为wgs84的gps坐标，如果要返回直接给openLocation用的火星坐标，可传入'gcj02'
        success: (res) => {
          resolve(res)
        },
        fail: (err) => {
          reject(err)
        }
      })
    })
  }

  /**
   * 选择图片
   */
  chooseImage(options = {}) {
    return new Promise((resolve, reject) => {
      if (!this.isWechat || !this.isConfigured) {
        reject(new Error('微信JS-SDK未初始化'))
        return
      }

      this.wx.chooseImage({
        count: options.count || 1, // 默认1张
        sizeType: options.sizeType || ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
        sourceType: options.sourceType || ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
        success: (res) => {
          resolve(res.localIds) // 返回选定照片的本地ID列表
        },
        fail: (err) => {
          reject(err)
        }
      })
    })
  }
}

// 创建单例
const wechatSDK = new WechatJSSDK()

export default wechatSDK
