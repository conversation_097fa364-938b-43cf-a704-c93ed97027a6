# 部署指南

本指南将帮助你快速部署微信JS-SDK集成的uniapp H5项目。

## 前端部署

### 1. 构建项目

```bash
# 安装依赖
npm install

# 构建H5版本
npm run build:h5
```

### 2. 配置域名

编辑 `utils/config.js`，设置正确的API域名：

```javascript
export const API_CONFIG = {
  production: {
    baseURL: 'https://your-api-domain.com', // 替换为你的实际API域名
    timeout: 10000
  }
}
```

### 3. 上传到服务器

将 `unpackage/dist/build/h5` 目录下的文件上传到你的Web服务器。

### 4. 配置HTTPS

确保你的网站支持HTTPS访问，微信JS-SDK要求必须使用HTTPS。

## 后端部署

### 1. 创建Node.js项目

```bash
mkdir wechat-api-server
cd wechat-api-server
npm init -y
```

### 2. 安装依赖

```bash
npm install express axios cors crypto
```

### 3. 复制API代码

将 `docs/backend-api-example.js` 的内容复制到你的后端项目中。

### 4. 配置环境变量

创建 `.env` 文件：

```env
WECHAT_APP_ID=wx50fb2779cc75a41f
WECHAT_APP_SECRET=8888
PORT=3000
NODE_ENV=production
```

### 5. 启动服务

```bash
# 开发环境
node server.js

# 生产环境（推荐使用PM2）
npm install -g pm2
pm2 start server.js --name wechat-api
```

## 微信公众平台配置

### 1. 登录微信公众平台

访问 https://mp.weixin.qq.com/ 并登录你的公众号。

### 2. 配置JS接口安全域名

1. 进入 "设置与开发" → "公众号设置" → "功能设置"
2. 找到 "JS接口安全域名"
3. 添加你的域名（不包含http://或https://）
   - 例如：`your-domain.com`

### 3. 获取开发者信息

1. 进入 "设置与开发" → "基本配置"
2. 记录你的 AppID 和 AppSecret
3. 更新配置文件中的相应信息

## 测试部署

### 1. 访问测试页面

在微信中打开：`https://your-domain.com/pages/wxTest/index`

### 2. 检查功能

- 环境检测是否正确
- SDK初始化是否成功
- 各项功能是否正常工作

### 3. 调试问题

如果遇到问题，检查：

1. **签名错误**
   - 确认URL正确（不包含#后面的部分）
   - 检查时间戳和随机字符串生成
   - 验证签名算法

2. **域名配置**
   - 确认JS接口安全域名配置正确
   - 检查是否使用HTTPS

3. **网络问题**
   - 检查API服务器是否正常运行
   - 确认CORS配置正确

## 生产环境优化

### 1. 缓存优化

在后端实现access_token和jsapi_ticket的缓存：

```javascript
// 使用Redis缓存
const redis = require('redis')
const client = redis.createClient()

// 缓存access_token
await client.setex('wechat_access_token', 7200, accessToken)
```

### 2. 错误监控

添加错误监控和日志记录：

```javascript
const winston = require('winston')

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
})
```

### 3. 安全配置

- 使用环境变量管理敏感信息
- 配置请求频率限制
- 添加IP白名单（如果需要）

### 4. 性能优化

- 启用Gzip压缩
- 配置CDN加速
- 优化图片和静态资源

## 常见问题解决

### Q: 签名验证失败
A: 检查URL、时间戳、随机字符串和签名生成算法

### Q: 在微信外无法使用功能
A: 这是正常的，微信JS-SDK只能在微信浏览器中使用

### Q: 某些功能权限不足
A: 检查微信公众号类型和权限，确认jsApiList配置正确

## 监控和维护

### 1. 日志监控

定期检查服务器日志，关注：
- API调用频率
- 错误率
- 响应时间

### 2. 功能测试

定期访问测试页面，确保各项功能正常。

### 3. 更新维护

- 定期更新依赖包
- 关注微信JS-SDK版本更新
- 备份重要配置和数据

## 技术支持

如需技术支持，请提供：
1. 错误信息和日志
2. 配置信息（隐藏敏感信息）
3. 复现步骤
4. 环境信息（浏览器、微信版本等）
