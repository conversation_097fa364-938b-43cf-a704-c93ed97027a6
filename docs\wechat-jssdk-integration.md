# 微信JS-SDK集成指南

本文档说明如何在uniapp H5项目中集成微信公众号JS-SDK功能。

## 功能特性

- ✅ 自动检测微信浏览器环境
- ✅ 动态加载微信JS-SDK
- ✅ 自动获取签名配置
- ✅ 扫码功能（微信扫码 + uni-app原生扫码兼容）
- ✅ 地理位置获取
- ✅ 图片选择和上传
- ✅ 错误处理和状态管理

## 项目结构

```
├── utils/
│   ├── wechat.js          # 微信JS-SDK工具类
│   └── config.js          # 配置文件
├── api/
│   └── wechat.js          # 微信API接口
├── pages/
│   └── scanWater/
│       └── index.vue      # 扫码用水页面（示例）
└── docs/
    ├── backend-api-example.js    # 后端API示例
    └── wechat-jssdk-integration.md  # 本文档
```

## 快速开始

### 1. 配置微信公众号信息

编辑 `utils/config.js` 文件：

```javascript
export const WECHAT_CONFIG = {
  appId: 'wx50fb2779cc75a41f', // 替换为你的微信公众号AppID
  // 注意：AppSecret不应该在前端暴露，应该在后端使用
}
```

### 2. 配置API域名

编辑 `utils/config.js` 中的API配置：

```javascript
export const API_CONFIG = {
  production: {
    baseURL: 'https://your-api-domain.com', // 替换为你的API域名
    timeout: 10000
  }
}
```

### 3. 部署后端API

参考 `docs/backend-api-example.js` 实现后端API接口：

- `POST /api/wechat/getJsApiTicket` - 获取JS-SDK配置信息
- `GET /api/wechat/getAccessToken` - 获取access_token（调试用）

### 4. 在页面中使用

```vue
<template>
  <view class="scan-container">
    <view class="scan-btn" @click="scanCode">扫码</view>
  </view>
</template>

<script setup>
import wechatSDK from '@/utils/wechat.js'
import { onLoad } from '@dcloudio/uni-app'

onLoad(async () => {
  // 初始化微信JS-SDK
  try {
    await wechatSDK.init()
    console.log('微信JS-SDK初始化成功')
  } catch (error) {
    console.error('微信JS-SDK初始化失败:', error)
  }
})

const scanCode = async () => {
  try {
    const result = await wechatSDK.scanQRCode()
    console.log('扫码结果:', result)
  } catch (error) {
    console.error('扫码失败:', error)
  }
}
</script>
```

## API文档

### WechatJSSDK 类

#### 方法

##### `init()`
初始化微信JS-SDK

```javascript
await wechatSDK.init()
```

##### `scanQRCode(options)`
扫码功能

```javascript
const result = await wechatSDK.scanQRCode({
  needResult: true,                    // 是否返回扫码结果
  scanType: ["qrCode", "barCode"]     // 扫码类型
})
```

##### `getLocation()`
获取地理位置

```javascript
const location = await wechatSDK.getLocation()
console.log(location.latitude, location.longitude)
```

##### `chooseImage(options)`
选择图片

```javascript
const images = await wechatSDK.chooseImage({
  count: 1,                           // 选择图片数量
  sizeType: ['original', 'compressed'], // 图片大小
  sourceType: ['album', 'camera']     // 图片来源
})
```

## 环境兼容性

### 微信浏览器环境
- 自动检测微信浏览器
- 使用微信JS-SDK原生功能
- 更好的用户体验

### 非微信浏览器环境
- 自动降级到uni-app原生功能
- 保证功能可用性
- 跨平台兼容

## 错误处理

### 常见错误及解决方案

1. **签名错误 (invalid signature)**
   - 检查URL是否正确（不包含#后面的部分）
   - 确认timestamp、nonceStr、signature生成正确
   - 检查jsapi_ticket是否有效

2. **配置失败**
   - 检查appId是否正确
   - 确认域名已在微信公众平台配置
   - 检查网络连接

3. **权限错误**
   - 确认jsApiList中包含所需权限
   - 检查微信公众号是否有相应权限

## 调试技巧

### 开启调试模式

编辑 `utils/config.js`：

```javascript
export const APP_CONFIG = {
  features: {
    debugMode: true  // 开启微信JS-SDK调试模式
  }
}
```

### 查看调试信息

在微信开发者工具或微信浏览器中查看控制台输出。

## 部署注意事项

### 1. HTTPS要求
微信JS-SDK要求页面必须通过HTTPS访问（本地调试除外）。

### 2. 域名配置
在微信公众平台后台配置JS接口安全域名：
- 公众号设置 → 功能设置 → JS接口安全域名

### 3. 服务器配置
- 确保服务器支持CORS
- 配置正确的Content-Type
- 实现access_token和jsapi_ticket缓存

## 常见问题

### Q: 为什么在微信外打开页面功能正常，在微信内打开失败？
A: 检查是否正确配置了JS接口安全域名，确保签名生成正确。

### Q: 如何测试微信JS-SDK功能？
A: 可以使用微信开发者工具的公众号网页调试功能进行测试。

### Q: 扫码功能在某些Android设备上不工作？
A: 确保微信版本支持JS-SDK，建议升级到最新版本。

## 更新日志

- v1.0.0: 初始版本，支持扫码、定位、图片选择等基础功能
- 支持自动环境检测和降级处理
- 完善的错误处理和状态管理

## 技术支持

如有问题，请检查：
1. 微信公众平台配置
2. 后端API实现
3. 网络连接状态
4. 浏览器控制台错误信息
